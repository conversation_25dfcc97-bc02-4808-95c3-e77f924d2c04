package com.phodal.ai;

import com.phodal.ai.migrator.JavaApiMigrator;
import com.phodal.ai.migrator.MavenDependencyUpgrader;
import org.junit.jupiter.api.Test;
import org.openrewrite.ExecutionContext;
import org.openrewrite.InMemoryExecutionContext;
import org.openrewrite.SourceFile;
import org.openrewrite.java.JavaParser;
import org.openrewrite.maven.MavenParser;

import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class AppTest {
    @Test
    void testJavaApiMigration() {
        String source = "import javax.servlet.http.HttpServlet;\n" +
                        "public class MyServlet extends HttpServlet { }";

        ExecutionContext ctx = new InMemoryExecutionContext(Throwable::printStackTrace);
        JavaParser javaParser = JavaParser.fromJavaVersion().build();
        List<SourceFile> compilationUnits = javaParser.parse(ctx, source).collect(Collectors.toList());

        assertNotNull(compilationUnits);
        assertEquals(1, compilationUnits.size());

        // As per current JavaApiMigrator, it only identifies and prints, not modifies.
        // So, the migrated source should be the same as original.
        SourceFile changed = (SourceFile) new JavaApiMigrator().visit(compilationUnits.get(0), ctx);

        assertNotNull(changed);
        assertEquals(source, changed.printAll());
    }

    @Test
    void testMavenDependencyUpgrade() {
        String pomXml = "<project>\n" +
                        "  <groupId>com.example</groupId>\n" +
                        "  <artifactId>my-app</artifactId>\n" +
                        "  <version>1.0.0</version>\n" +
                        "  <dependencies>\n" +
                        "    <dependency>\n" +
                        "      <groupId>org.springframework.boot</groupId>\n" +
                        "      <artifactId>spring-boot-starter</artifactId>\n" +
                        "      <version>2.7.0</version>\n" +
                        "    </dependency>\n" +
                        "  </dependencies>\n" +
                        "</project>";

        String expectedPomXml = "<project>\n" +
                                "  <groupId>com.example</groupId>\n" +
                                "  <artifactId>my-app</artifactId>\n" +
                                "  <version>1.0.0</version>\n" +
                                "  <dependencies>\n" +
                                "    <dependency>\n" +
                                "      <groupId>org.springframework.boot</groupId>\n" +
                                "      <artifactId>spring-boot-starter</artifactId>\n" +
                                "      <version>3.2.0</version>\n" +
                                "    </dependency>\n" +
                                "  </dependencies>\n" +
                                "</project>";

        ExecutionContext ctx = new InMemoryExecutionContext(Throwable::printStackTrace);
        MavenParser mavenParser = MavenParser.builder().build();
        List<SourceFile> mavens = mavenParser.parse(ctx, pomXml).collect(Collectors.toList());

        assertNotNull(mavens);
        assertEquals(1, mavens.size());

        SourceFile changed = (SourceFile) new MavenDependencyUpgrader(
                "org.springframework.boot",
                "spring-boot-starter",
                "3.2.0"
        ).visit(mavens.get(0), ctx);

        assertNotNull(changed);
        assertEquals(expectedPomXml, changed.printAll());
    }
}