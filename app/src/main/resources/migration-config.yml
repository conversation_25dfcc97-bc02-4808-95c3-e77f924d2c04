# Spring Boot 迁移工具配置文件

migration:
  # 目标版本配置
  target-versions:
    spring-boot: "3.2.0"
    java: "21"
  
  # 迁移规则配置
  rules:
    - name: "javax-to-jakarta"
      enabled: true
      priority: 1
      parameters:
        exclude-packages: []
    
    - name: "spring-security-config"
      enabled: true
      priority: 2
      parameters:
        update-method-security: true
    
    - name: "spring-boot-properties"
      enabled: true
      priority: 3
      parameters:
        remove-deprecated: true
    
    - name: "jdk21-features"
      enabled: false
      priority: 10
      parameters:
        apply-pattern-matching: true
        apply-text-blocks: true
  
  # AI服务配置
  ai:
    provider: "phodal"
    max-retries: 3
    timeout-seconds: 30
    # api-key: "your-api-key-here"
  
  # 构建配置
  build:
    command: "mvn clean compile"
    max-attempts: 5
    skip-tests:
      - "integration-tests"
  
  # 报告配置
  report:
    formats: ["markdown", "json"]
    output-dir: "migration-reports"
    include-details: true
