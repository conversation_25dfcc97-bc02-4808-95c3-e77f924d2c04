package com.phodal.migrator.cli;

import com.phodal.migrator.JavaAutoMigrator;
import com.phodal.migrator.config.ConfigLoader;
import com.phodal.migrator.config.MigrationConfig;
import com.phodal.migrator.core.MigrationOptions;
import com.phodal.migrator.core.MigrationResult;
import picocli.CommandLine;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.Parameters;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

/**
 * Spring Boot 2.x → 3.x + JDK 8 → 21 迁移工具 CLI
 */
@Command(
    name = "java-migrator",
    description = "Spring Boot 2.x → 3.x + JDK 8 → 21 迁移工具",
    version = "1.0.0",
    mixinStandardHelpOptions = true,
    subcommands = {
        JavaMigratorCLI.MigrateCommand.class,
        JavaMigratorCLI.AnalyzeCommand.class
    }
)
public class JavaMigratorCLI implements Callable<Integer> {
    
    public static void main(String[] args) {
        int exitCode = new CommandLine(new JavaMigratorCLI()).execute(args);
        System.exit(exitCode);
    }
    
    @Override
    public Integer call() {
        // 显示帮助信息
        CommandLine.usage(this, System.out);
        return 0;
    }
    
    /**
     * 迁移命令
     */
    @Command(
        name = "migrate",
        description = "执行完整的迁移流程"
    )
    static class MigrateCommand implements Callable<Integer> {
        
        @Parameters(
            index = "0",
            description = "项目路径"
        )
        private String projectPath;
        
        @Option(
            names = {"--target-path"},
            description = "目标路径 (可选，默认就地迁移)"
        )
        private String targetPath;
        
        @Option(
            names = {"--dry-run"},
            description = "预览模式，不实际修改文件"
        )
        private boolean dryRun = false;
        
        @Option(
            names = {"-v", "--verbose"},
            description = "详细输出模式"
        )
        private boolean verbose = false;
        
        @Option(
            names = {"--enable-ai-fix"},
            description = "启用AI修复功能"
        )
        private boolean enableAiFix = false;
        
        @Option(
            names = {"--max-build-attempts"},
            description = "最大构建尝试次数 (默认: 3)"
        )
        private int maxBuildAttempts = 3;
        
        @Option(
            names = {"--skip-steps"},
            description = "跳过的步骤 (逗号分隔): config,test,runtime",
            split = ","
        )
        private String[] skipSteps = {};
        
        @Option(
            names = {"--build-command"},
            description = "自定义构建命令"
        )
        private String buildCommand;
        
        @Option(
            names = {"--report-format"},
            description = "报告格式 (逗号分隔): markdown,json,html",
            split = ","
        )
        private String[] reportFormats = {"markdown"};
        
        @Option(
            names = {"--ai-provider"},
            description = "AI服务提供商 (默认: phodal)"
        )
        private String aiProvider = "phodal";

        @Option(
            names = {"--config", "-c"},
            description = "配置文件路径"
        )
        private String configFile;
        
        @Override
        public Integer call() {
            try {
                System.out.println("🚀 Spring Boot 2.x → 3.x + JDK 8 → 21 迁移工具");
                System.out.println("================================================");
                
                // 加载配置文件
                MigrationConfig config = null;
                if (configFile != null) {
                    config = ConfigLoader.loadConfig(Paths.get(configFile));
                } else {
                    config = ConfigLoader.loadConfig(null);
                }

                // 构建迁移选项
                MigrationOptions options = MigrationOptions.builder()
                        .dryRun(dryRun)
                        .verbose(verbose)
                        .enableAiFix(enableAiFix)
                        .maxBuildAttempts(maxBuildAttempts)
                        .skipSteps(Set.of(skipSteps))
                        .buildCommand(buildCommand)
                        .reportFormats(Arrays.asList(reportFormats))
                        .aiProvider(aiProvider)
                        .config(config)
                        .build();
                
                // 创建迁移器
                Path projectDir = Paths.get(projectPath);
                Path targetDir = targetPath != null ? Paths.get(targetPath) : projectDir;
                
                JavaAutoMigrator migrator = new JavaAutoMigrator(projectDir, targetDir, options);
                
                // 执行迁移
                MigrationResult result = migrator.migrate();
                
                // 输出结果
                if (result.isSuccess()) {
                    System.out.println("\n✅ 迁移成功完成！");
                    System.out.printf("总耗时: %d 秒\n", result.getTotalDuration().getSeconds());
                    
                    if (verbose) {
                        printDetailedStats(result);
                    }
                    
                    return 0;
                } else {
                    System.err.println("\n❌ 迁移失败: " + result.getMessage());
                    
                    if (verbose) {
                        printDetailedStats(result);
                    }
                    
                    return 1;
                }
                
            } catch (Exception e) {
                System.err.println("❌ 执行失败: " + e.getMessage());
                if (verbose) {
                    e.printStackTrace();
                }
                return 1;
            }
        }
        
        private void printDetailedStats(MigrationResult result) {
            System.out.println("\n📊 详细统计:");
            System.out.printf("- 分析文件: %d\n", result.getStats().getFilesAnalyzed());
            System.out.printf("- 修改文件: %d\n", result.getStats().getFilesModified());
            System.out.printf("- 升级依赖: %d\n", result.getStats().getDependenciesUpgraded());
            System.out.printf("- 修复错误: %d\n", result.getStats().getErrorsFixed());
            System.out.printf("- 运行测试: %d\n", result.getStats().getTestsRun());
            System.out.printf("- 通过测试: %d\n", result.getStats().getTestsPassed());
        }
    }
    
    /**
     * 分析命令
     */
    @Command(
        name = "analyze",
        description = "仅分析项目，不执行迁移"
    )
    static class AnalyzeCommand implements Callable<Integer> {
        
        @Parameters(
            index = "0",
            description = "项目路径"
        )
        private String projectPath;
        
        @Option(
            names = {"-v", "--verbose"},
            description = "详细输出模式"
        )
        private boolean verbose = false;
        
        @Override
        public Integer call() {
            try {
                System.out.println("🔍 项目分析模式");
                System.out.println("================");
                
                // 构建分析选项
                MigrationOptions options = MigrationOptions.builder()
                        .dryRun(true)
                        .verbose(verbose)
                        .skipSteps(Set.of("config_migration", "dependency_migration", 
                                         "code_migration", "build_fix", "test_execution", 
                                         "runtime_validation", "report_generation"))
                        .build();
                
                // 创建迁移器并执行分析
                Path projectDir = Paths.get(projectPath);
                JavaAutoMigrator migrator = new JavaAutoMigrator(projectDir, options);
                
                MigrationResult result = migrator.migrate();
                
                if (result.isSuccess()) {
                    System.out.println("\n✅ 项目分析完成！");
                    return 0;
                } else {
                    System.err.println("\n❌ 项目分析失败: " + result.getMessage());
                    return 1;
                }
                
            } catch (Exception e) {
                System.err.println("❌ 分析失败: " + e.getMessage());
                if (verbose) {
                    e.printStackTrace();
                }
                return 1;
            }
        }
    }
}
