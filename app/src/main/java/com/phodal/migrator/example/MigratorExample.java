package com.phodal.migrator.example;

import com.phodal.migrator.JavaAutoMigrator;
import com.phodal.migrator.core.MigrationOptions;
import com.phodal.migrator.core.MigrationResult;

import java.nio.file.Paths;

/**
 * 迁移工具使用示例
 */
public class MigratorExample {
    
    public static void main(String[] args) {
        // 示例1: 基本迁移
        basicMigrationExample();
        
        // 示例2: 预览模式
        dryRunExample();
        
        // 示例3: 自定义选项
        customOptionsExample();
    }
    
    /**
     * 基本迁移示例
     */
    private static void basicMigrationExample() {
        System.out.println("=== 基本迁移示例 ===");
        
        try {
            // 创建默认选项
            MigrationOptions options = MigrationOptions.builder()
                    .verbose(true)
                    .build();
            
            // 创建迁移器
            JavaAutoMigrator migrator = new JavaAutoMigrator(
                    Paths.get("_fixtures/test-springboot-app"),
                    options
            );
            
            // 执行迁移
            MigrationResult result = migrator.migrate();
            
            // 输出结果
            if (result.isSuccess()) {
                System.out.println("✅ 迁移成功！");
                System.out.printf("耗时: %d 秒\n", result.getTotalDuration().getSeconds());
            } else {
                System.err.println("❌ 迁移失败: " + result.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 预览模式示例
     */
    private static void dryRunExample() {
        System.out.println("\n=== 预览模式示例 ===");
        
        try {
            // 创建预览模式选项
            MigrationOptions options = MigrationOptions.builder()
                    .dryRun(true)
                    .verbose(true)
                    .build();
            
            // 创建迁移器
            JavaAutoMigrator migrator = new JavaAutoMigrator(
                    Paths.get("_fixtures/test-springboot-app"),
                    options
            );
            
            // 执行预览
            MigrationResult result = migrator.migrate();
            
            System.out.println("🔍 预览完成，未实际修改文件");
            
        } catch (Exception e) {
            System.err.println("预览失败: " + e.getMessage());
        }
    }
    
    /**
     * 自定义选项示例
     */
    private static void customOptionsExample() {
        System.out.println("\n=== 自定义选项示例 ===");
        
        try {
            // 创建自定义选项
            MigrationOptions options = MigrationOptions.builder()
                    .dryRun(true)
                    .verbose(true)
                    .enableAiFix(true)
                    .maxBuildAttempts(5)
                    .skipSteps(java.util.Set.of("test_execution", "runtime_validation"))
                    .reportFormats(java.util.List.of("markdown", "json"))
                    .build();
            
            // 创建迁移器
            JavaAutoMigrator migrator = new JavaAutoMigrator(
                    Paths.get("_fixtures/test-springboot-app"),
                    options
            );
            
            // 执行迁移
            MigrationResult result = migrator.migrate();
            
            System.out.println("🎯 自定义迁移完成");
            System.out.printf("跳过步骤: %s\n", options.getSkipSteps());
            
        } catch (Exception e) {
            System.err.println("自定义迁移失败: " + e.getMessage());
        }
    }
}
