package com.phodal.migrator.steps;

import com.phodal.migrator.core.MigrationContext;
import com.phodal.migrator.core.MigrationStep;
import com.phodal.migrator.core.StepResult;
import com.phodal.migrator.core.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;

/**
 * 抽象迁移步骤基类
 * 提供通用的步骤执行框架
 */
public abstract class AbstractMigrationStep implements MigrationStep {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    @Override
    public final StepResult execute(MigrationContext context) {
        Instant startTime = Instant.now();
        
        try {
            logger.debug("开始执行步骤: {}", getName());
            
            // 执行具体的步骤逻辑
            StepResult result = doExecute(context);
            
            Instant endTime = Instant.now();
            logger.debug("步骤执行完成: {} (耗时: {}ms)", getName(), 
                        java.time.Duration.between(startTime, endTime).toMillis());
            
            // 直接返回原始结果，只更新时间信息
            if (result.isSuccess()) {
                return StepResult.builder()
                        .success(true)
                        .message(result.getMessage())
                        .details(result.getDetails())
                        .startTime(startTime)
                        .endTime(endTime)
                        .type(result.getType())
                        .build();
            } else {
                return StepResult.builder()
                        .success(false)
                        .message(result.getMessage())
                        .error(result.getError())
                        .details(result.getDetails())
                        .startTime(startTime)
                        .endTime(endTime)
                        .type(result.getType())
                        .build();
            }
                    
        } catch (Exception e) {
            Instant endTime = Instant.now();
            logger.error("步骤执行失败: {}", getName(), e);
            
            return StepResult.builder()
                    .success(false)
                    .message("步骤执行失败: " + e.getMessage())
                    .error(e)
                    .startTime(startTime)
                    .endTime(endTime)
                    .build();
        }
    }
    
    /**
     * 子类实现具体的步骤逻辑
     */
    protected abstract StepResult doExecute(MigrationContext context);
    
    @Override
    public ValidationResult validate(MigrationContext context) {
        return ValidationResult.success();
    }
    
    @Override
    public boolean isRequired() {
        return true; // 默认为必需步骤
    }
    
    @Override
    public int getEstimatedDurationSeconds() {
        return 60; // 默认预估1分钟
    }
}
