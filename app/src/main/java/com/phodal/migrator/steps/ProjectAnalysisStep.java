package com.phodal.migrator.steps;

import com.phodal.migrator.core.MigrationContext;
import com.phodal.migrator.core.ProjectType;
import com.phodal.migrator.core.StepResult;
import com.phodal.migrator.service.analysis.ProjectAnalysisService;
import com.phodal.migrator.service.analysis.ProjectAnalysisServiceImpl;
import com.phodal.migrator.core.ValidationResult;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 步骤1: 项目分析与备份
 * 
 * 功能：
 * - 分析项目结构和技术栈
 * - 检测当前Spring Boot和JDK版本
 * - 创建项目备份
 * - 生成项目分析报告
 */
public class ProjectAnalysisStep extends AbstractMigrationStep {

    private final ProjectAnalysisService analysisService;

    public ProjectAnalysisStep() {
        this.analysisService = new ProjectAnalysisServiceImpl();
    }
    
    @Override
    public String getName() {
        return "project_analysis";
    }
    
    @Override
    public String getDescription() {
        return "分析项目结构，检测版本信息，创建备份";
    }
    
    @Override
    public int getEstimatedDurationSeconds() {
        return 30;
    }
    
    @Override
    public ValidationResult validate(MigrationContext context) {
        ValidationResult.Builder builder = ValidationResult.builder();
        
        Path projectPath = context.getProjectPath();
        
        // 检查项目路径是否存在
        if (!Files.exists(projectPath)) {
            builder.addError("项目路径不存在: " + projectPath);
        }
        
        // 检查是否为目录
        if (!Files.isDirectory(projectPath)) {
            builder.addError("项目路径不是目录: " + projectPath);
        }
        
        // 检查是否有读取权限
        if (!Files.isReadable(projectPath)) {
            builder.addError("项目路径不可读: " + projectPath);
        }
        
        return builder.build();
    }
    
    @Override
    protected StepResult doExecute(MigrationContext context) {
        Map<String, Object> analysisResult = new HashMap<>();
        
        try {
            // 1. 分析项目类型
            ProjectType projectType = analyzeProjectType(context);
            analysisResult.put("project_type", projectType.getDisplayName());
            
            // 2. 检测Spring Boot版本
            String springBootVersion = detectSpringBootVersion(context);
            context.setCurrentSpringBootVersion(springBootVersion);
            analysisResult.put("current_spring_boot_version", springBootVersion);
            
            // 3. 检测Java版本
            String javaVersion = detectJavaVersion(context);
            context.setCurrentJavaVersion(javaVersion);
            analysisResult.put("current_java_version", javaVersion);
            
            // 4. 分析项目结构
            ProjectStructure structure = analyzeProjectStructure(context);
            analysisResult.put("project_structure", structure);
            
            // 5. 创建备份（如果不是预览模式）
            if (!context.isDryRun()) {
                Path backupPath = createBackup(context);
                analysisResult.put("backup_path", backupPath.toString());
                logger.info("✅ 项目备份已创建: {}", backupPath);
            } else {
                logger.info("🔍 预览模式：跳过备份创建");
            }
            
            // 6. 设置目标版本
            context.setTargetSpringBootVersion("3.2.0");
            context.setTargetJavaVersion("21");
            
            return StepResult.success("项目分析完成")
                    .addDetail("analysis_result", analysisResult);
                    
        } catch (Exception e) {
            return StepResult.failure("项目分析失败: " + e.getMessage(), e);
        }
    }
    
    private ProjectType analyzeProjectType(MigrationContext context) {
        Path projectPath = context.getProjectPath();
        
        if (Files.exists(projectPath.resolve("pom.xml"))) {
            return ProjectType.MAVEN;
        } else if (Files.exists(projectPath.resolve("build.gradle.kts"))) {
            return ProjectType.GRADLE_KTS;
        } else if (Files.exists(projectPath.resolve("build.gradle"))) {
            return ProjectType.GRADLE;
        }
        
        return ProjectType.UNKNOWN;
    }
    
    private String detectSpringBootVersion(MigrationContext context) {
        // TODO: 实现Spring Boot版本检测逻辑
        // 从pom.xml或build.gradle中解析版本信息
        return "2.7.0"; // 临时返回
    }
    
    private String detectJavaVersion(MigrationContext context) {
        // TODO: 实现Java版本检测逻辑
        // 从构建文件或.java文件中检测Java版本
        return "8"; // 临时返回
    }
    
    private ProjectStructure analyzeProjectStructure(MigrationContext context) {
        // TODO: 实现项目结构分析
        return new ProjectStructure();
    }
    
    private Path createBackup(MigrationContext context) throws IOException {
        Path projectPath = context.getProjectPath();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        Path backupPath = projectPath.getParent().resolve(projectPath.getFileName() + "_backup_" + timestamp);
        
        // 递归复制整个项目目录
        copyDirectory(projectPath, backupPath);
        
        return backupPath;
    }
    
    private void copyDirectory(Path source, Path target) throws IOException {
        Files.walk(source)
                .forEach(sourcePath -> {
                    try {
                        Path targetPath = target.resolve(source.relativize(sourcePath));
                        if (Files.isDirectory(sourcePath)) {
                            Files.createDirectories(targetPath);
                        } else {
                            Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                        }
                    } catch (IOException e) {
                        throw new RuntimeException("备份文件失败: " + sourcePath, e);
                    }
                });
    }
    
    // 项目结构分析结果
    public static class ProjectStructure {
        private int javaFiles;
        private int configFiles;
        private int testFiles;
        private boolean hasSpringBoot;
        private boolean hasSpringMvc;
        private boolean hasSpringData;
        
        // Getters and setters
        public int getJavaFiles() { return javaFiles; }
        public void setJavaFiles(int javaFiles) { this.javaFiles = javaFiles; }
        
        public int getConfigFiles() { return configFiles; }
        public void setConfigFiles(int configFiles) { this.configFiles = configFiles; }
        
        public int getTestFiles() { return testFiles; }
        public void setTestFiles(int testFiles) { this.testFiles = testFiles; }
        
        public boolean isHasSpringBoot() { return hasSpringBoot; }
        public void setHasSpringBoot(boolean hasSpringBoot) { this.hasSpringBoot = hasSpringBoot; }
        
        public boolean isHasSpringMvc() { return hasSpringMvc; }
        public void setHasSpringMvc(boolean hasSpringMvc) { this.hasSpringMvc = hasSpringMvc; }
        
        public boolean isHasSpringData() { return hasSpringData; }
        public void setHasSpringData(boolean hasSpringData) { this.hasSpringData = hasSpringData; }
    }
}
