package com.phodal.migrator.core;

import java.util.List;
import java.util.Set;

/**
 * 迁移选项配置
 */
public class MigrationOptions {
    private final boolean dryRun;
    private final boolean verbose;
    private final boolean enableAiFix;
    private final int maxBuildAttempts;
    private final Set<String> skipSteps;
    private final String buildCommand;
    private final List<String> reportFormats;
    private final String aiProvider;
    
    private MigrationOptions(Builder builder) {
        this.dryRun = builder.dryRun;
        this.verbose = builder.verbose;
        this.enableAiFix = builder.enableAiFix;
        this.maxBuildAttempts = builder.maxBuildAttempts;
        this.skipSteps = builder.skipSteps;
        this.buildCommand = builder.buildCommand;
        this.reportFormats = builder.reportFormats;
        this.aiProvider = builder.aiProvider;
    }
    
    public boolean isDryRun() {
        return dryRun;
    }
    
    public boolean isVerbose() {
        return verbose;
    }
    
    public boolean isEnableAiFix() {
        return enableAiFix;
    }
    
    public int getMaxBuildAttempts() {
        return maxBuildAttempts;
    }
    
    public Set<String> getSkipSteps() {
        return skipSteps;
    }
    
    public String getBuildCommand() {
        return buildCommand;
    }
    
    public List<String> getReportFormats() {
        return reportFormats;
    }
    
    public String getAiProvider() {
        return aiProvider;
    }
    
    public boolean shouldSkipStep(String stepName) {
        return skipSteps.contains(stepName);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private boolean dryRun = false;
        private boolean verbose = false;
        private boolean enableAiFix = false;
        private int maxBuildAttempts = 3;
        private Set<String> skipSteps = Set.of();
        private String buildCommand = null;
        private List<String> reportFormats = List.of("markdown");
        private String aiProvider = "phodal";
        
        public Builder dryRun(boolean dryRun) {
            this.dryRun = dryRun;
            return this;
        }
        
        public Builder verbose(boolean verbose) {
            this.verbose = verbose;
            return this;
        }
        
        public Builder enableAiFix(boolean enableAiFix) {
            this.enableAiFix = enableAiFix;
            return this;
        }
        
        public Builder maxBuildAttempts(int maxBuildAttempts) {
            this.maxBuildAttempts = maxBuildAttempts;
            return this;
        }
        
        public Builder skipSteps(Set<String> skipSteps) {
            this.skipSteps = skipSteps;
            return this;
        }
        
        public Builder buildCommand(String buildCommand) {
            this.buildCommand = buildCommand;
            return this;
        }
        
        public Builder reportFormats(List<String> reportFormats) {
            this.reportFormats = reportFormats;
            return this;
        }
        
        public Builder aiProvider(String aiProvider) {
            this.aiProvider = aiProvider;
            return this;
        }
        
        public MigrationOptions build() {
            return new MigrationOptions(this);
        }
    }
}
